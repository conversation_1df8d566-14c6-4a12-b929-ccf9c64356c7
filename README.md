# Notion MCP Server

Notion API 用の MCP サーバーで、Claude が Notion ワークスペースと対話できるようにします。

## セットアップ

以下の記事で、上記の手順について詳細に説明しています。

- 英語バージョン: https://dev.to/suekou/operating-notion-via-claude-desktop-using-mcp-c0h
- 日本語バージョン: https://qiita.com/suekou/items/44c864583f5e3e6325d9

1.  **Notion インテグレーションを作成**:

    - [Notion Your Integrations ページ](https://www.notion.so/profile/integrations)にアクセスします。
    - 「New Integration」をクリックします。
    - インテグレーションに名前を付け、適切な権限（例：「コンテンツの読み取り」、「コンテンツの更新」）を選択します。

2.  **シークレットキーを取得**:

    - インテグレーションから「Internal Integration Token」をコピーします。
    - このトークンは認証に使用されます。

3.  **ワークスペースにインテグレーションを追加**:

    - インテグレーションにアクセスさせたいページまたはデータベースを Notion で開きます。
    - 右上の「···」ボタンをクリックします。
    - 「コネクション」ボタンをクリックし、ステップ 1 で作成したインテグレーションを選択します。

4.  **Claude Desktop を設定**:
    `claude_desktop_config.json` に以下を追加します。

```json
{
  "mcpServers": {
    "notion": {
      "command": "npx",
      "args": ["-y", "@suekou/mcp-notion-server"],
      "env": {
        "NOTION_API_TOKEN": "your-integration-token"
      }
    }
  }
}
```

または

```json
{
  "mcpServers": {
    "notion": {
      "command": "node",
      "args": ["your-built-file-path"],
      "env": {
        "NOTION_API_TOKEN": "your-integration-token"
      }
    }
  }
}
```

## 環境変数

- `NOTION_API_TOKEN` (必須): Notion API インテグレーショントークン。
- `NOTION_MARKDOWN_CONVERSION`: 実験的な Markdown 変換を有効にするには "true" に設定します。これにより、コンテンツ表示時のトークン消費量を大幅に削減できますが、ページコンテンツの編集時に問題が発生する可能性があります。

## 高度な設定

### Markdown 変換

デフォルトでは、すべての応答は JSON 形式で返されます。実験的な Markdown 変換を有効にして、トークン消費量を削減できます。

```json
{
  "mcpServers": {
    "notion": {
      "command": "npx",
      "args": ["-y", "@suekou/mcp-notion-server"],
      "env": {
        "NOTION_API_TOKEN": "your-integration-token",
        "NOTION_MARKDOWN_CONVERSION": "true"
      }
    }
  }
}
```

または

```json
{
  "mcpServers": {
    "notion": {
      "command": "node",
      "args": ["your-built-file-path"],
      "env": {
        "NOTION_API_TOKEN": "your-integration-token",
        "NOTION_MARKDOWN_CONVERSION": "true"
      }
    }
  }
}
```

`NOTION_MARKDOWN_CONVERSION` が `"true"` に設定されている場合、応答は Markdown 形式に変換されます（`format` パラメータが `"markdown"` に設定されている場合）。これにより、人間が読みやすい出力には "markdown" を、元のデータ構造へのプログラムアクセスには "json" を使用します。注意: Markdown 変換は、`NOTION_MARKDOWN_CONVERSION` 環境変数が "true" に設定されている場合にのみ機能します。

ツール呼び出しで `format` パラメータを `"json"` または `"markdown"` に設定することで、リクエストごとにフォーマットを制御できます。

- コンテンツの表示のみを行う場合は、読みやすさ向上のために `"markdown"` を使用します。
- 返されたコンテンツを変更する必要がある場合は `"json"` を使用します。

## トラブルシューティング

パーミッションエラーが発生した場合：

1.  インテグレーションに必要な権限があることを確認します。
2.  インテグレーションが関連するページまたはデータベースに招待されていることを確認します。
3.  トークンと設定が `claude_desktop_config.json` で正しく設定されていることを確認します。

## ツール

すべてのツールは、以下のオプションパラメータをサポートしています。

- `format` (文字列、 "json" または "markdown"、デフォルト: "markdown"): 応答形式を制御します。人間が読みやすい出力には "markdown" を、元のデータ構造へのプログラムアクセスには "json" を使用します。注意: Markdown 変換は、`NOTION_MARKDOWN_CONVERSION` 環境変数が "true" に設定されている場合にのみ機能します。

1.  `notion_append_block_children`

    - 親ブロックに子ブロックを追加します。
    - 必須入力:
      - `block_id` (文字列): 親ブロックの ID。
      - `children` (配列): 追加するブロックオブジェクトの配列。
    - 戻り値: 追加されたブロックに関する情報。

2.  `notion_retrieve_block`

    - 特定のブロックに関する情報を取得します。
    - 必須入力:
      - `block_id` (文字列): 取得するブロックの ID。
    - 戻り値: ブロックに関する詳細情報。

3.  `notion_retrieve_block_children`

    - 特定のブロックの子を取得します。
    - 必須入力:
      - `block_id` (文字列): 親ブロックの ID。
    - オプション入力:
      - `start_cursor` (文字列): 結果の次のページのカーソル。
      - `page_size` (数値、デフォルト: 100、最大: 100): 取得するブロックの数。
    - 戻り値: 子ブロックのリスト。

4.  `notion_delete_block`

    - 特定のブロックを削除します。
    - 必須入力:
      - `block_id` (文字列): 削除するブロックの ID。
    - 戻り値: 削除の確認。

5.  `notion_retrieve_page`

    - 特定のページに関する情報を取得します。
    - 必須入力:
      - `page_id` (文字列): 取得するページの ID。
    - 戻り値: ページに関する詳細情報。

6.  `notion_update_page_properties`

    - ページのプロパティを更新します。
    - 必須入力:
      - `page_id` (文字列): 更新するページの ID。
      - `properties` (オブジェクト): 更新するプロパティ。
    - 戻り値: 更新されたページに関する情報。

7.  `notion_create_database`

    - 新しいデータベースを作成します。
    - 必須入力:
      - `parent` (オブジェクト): データベースの親オブジェクト。
      - `title` (配列): リッチテキスト配列としてのデータベースのタイトル。
      - `properties` (オブジェクト): データベースのプロパティスキーマ。
    - 戻り値: 作成されたデータベースに関する情報。

8.  `notion_query_database`

    - データベースをクエリします。
    - 必須入力:
      - `database_id` (文字列): クエリするデータベースの ID。
    - オプション入力:
      - `filter` (オブジェクト): フィルタ条件。
      - `sorts` (配列): ソート条件。
      - `start_cursor` (文字列): 結果の次のページのカーソル。
      - `page_size` (数値、デフォルト: 100、最大: 100): 取得する結果の数。
    - 戻り値: クエリからの結果のリスト。

9.  `notion_retrieve_database`

    - 特定のデータベースに関する情報を取得します。
    - 必須入力:
      - `database_id` (文字列): 取得するデータベースの ID。
    - 戻り値: データベースに関する詳細情報。

10. `notion_update_database`

    - データベースに関する情報を更新します。
    - 必須入力:
      - `database_id` (文字列): 更新するデータベースの ID。
    - オプション入力:
      - `title` (配列): データベースの新しいタイトル。
      - `description` (配列): データベースの新しい説明。
      - `properties` (オブジェクト): 更新されたプロパティスキーマ。
    - 戻り値: 更新されたデータベースに関する情報。

11. `notion_create_database_item`

    - Notion データベースに新しいアイテムを作成します。
    - 必須入力:
      - `database_id` (文字列): アイテムを追加するデータベースの ID。
      - `properties` (オブジェクト): 新しいアイテムのプロパティ。これらはデータベーススキーマと一致する必要があります。
    - 戻り値: 新しく作成されたアイテムに関する情報。

12. `notion_search`

    - タイトルでページまたはデータベースを検索します。
    - オプション入力:
      - `query` (文字列): ページまたはデータベースのタイトルで検索するテキスト。
      - `filter` (オブジェクト): 結果をページのみまたはデータベースのみに制限する基準。
      - `sort` (オブジェクト): 結果をソートする基準。
      - `start_cursor` (文字列): ページネーションの開始カーソル。
      - `page_size` (数値、デフォルト: 100、最大: 100): 取得する結果の数。
    - 戻り値: 一致するページまたはデータベースのリスト。

13. `notion_list_all_users`

    - Notion ワークスペース内のすべてのユーザーをリストします。
    - 注意: この関数は、パーミッションエラーを回避するために、Notion エンタープライズプランにアップグレードし、Organization API キーを使用する必要があります。
    - オプション入力:
      - `start_cursor` (文字列): ユーザーをリストするためのページネーション開始カーソル。
      - `page_size` (数値、最大: 100): 取得するユーザーの数。
    - 戻り値: ワークスペース内のすべてのユーザーのページ分割されたリスト。

14. `notion_retrieve_user`

    - Notion で user_id によって特定のユーザーを取得します。
    - 注意: この関数は、パーミッションエラーを回避するために、Notion エンタープライズプランにアップグレードし、Organization API キーを使用する必要があります。
    - 必須入力:
      - `user_id` (文字列): 取得するユーザーの ID。
    - 戻り値: 指定されたユーザーに関する詳細情報。

15. `notion_retrieve_bot_user`

    - Notion の現在のトークンに関連付けられたボットユーザーを取得します。
    - 戻り値: インテグレーションを承認した人物の詳細を含む、ボットユーザーに関する情報。

16. `notion_create_comment`

    - Notion でコメントを作成します。
    - インテグレーションが「コメントの挿入」機能を持っている必要があります。
    - `page_id` を持つ `parent` オブジェクト、または `discussion_id` のいずれかを指定しますが、両方を指定することはできません。
    - 必須入力:
      - `rich_text` (配列): コメントの内容を表すリッチテキストオブジェクトの配列。
    - オプション入力:
      - `parent` (オブジェクト): 使用する場合は `page_id` を含める必要があります。
      - `discussion_id` (文字列): 既存のディスカッションスレッド ID。
    - 戻り値: 作成されたコメントに関する情報。

17. `notion_retrieve_comments`
    - Notion ページまたはブロックから未解決のコメントのリストを取得します。
    - インテグレーションが「コメントの読み取り」機能を持っている必要があります。
    - 必須入力:
      - `block_id` (文字列): コメントを取得したいブロックまたはページの ID。
    - オプション入力:
      - `start_cursor` (文字列): ページネーションの開始カーソル。
      - `page_size` (数値、最大: 100): 取得するコメントの数。
    - 戻り値: 指定されたブロックまたはページに関連付けられたコメントのページ分割されたリスト。

## ライセンス

この MCP サーバーは MIT ライセンスの下でライセンスされています。これは、MIT ライセンスの条件に従って、ソフトウェアを自由に使用、変更、配布できることを意味します。詳細については、プロジェクトリポジトリの LICENSE ファイルを参照してください。
