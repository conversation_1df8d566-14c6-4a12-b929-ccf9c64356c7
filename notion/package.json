{"name": "@suekou/mcp-notion-server", "version": "1.1.1", "description": "MCP server for interacting with Notion API based on Node", "license": "MIT", "author": "<PERSON><PERSON> (https://github.com/suekou/mcp-notion-server)", "type": "module", "main": "build/index.js", "bin": {"mcp-notion-server": "./build/index.js"}, "files": ["build", "Readme.md", "notion"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "prepare": "npm run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector build/index.js", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "tsc": "^2.0.4", "vitest": "3.0.9"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.8.2"}}